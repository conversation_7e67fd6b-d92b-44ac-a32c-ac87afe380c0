/**
 * Live2D 辅助工具函数
 * 提供便捷的 Live2D 操作接口
 */

import { ModelInfo, AudioTaskOptions } from '@/types/live2d';
import { 
  getLive2DManager, 
  getLAppAdapter, 
  setModelExpression,
  startRandomMotion,
  getLAppDefine 
} from './sdk-wrapper';
import { audioTaskQueue } from './audio-task-queue';
import { createAudioDataUrl, isValidAudioBase64 } from './utils';

/**
 * Live2D 辅助类
 * 提供高级 Live2D 操作接口
 */
export class Live2DHelper {
  /**
   * 检查 Live2D 是否准备就绪
   */
  static isReady(): boolean {
    const manager = getLive2DManager();
    const adapter = getLAppAdapter();
    return !!(manager && adapter);
  }

  /**
   * 获取当前模型
   */
  static getCurrentModel(index = 0) {
    const manager = getLive2DManager();
    return manager?.getModel(index) || null;
  }

  /**
   * 设置模型表情
   */
  static setExpression(expression: string | number): boolean {
    return setModelExpression(expression);
  }

  /**
   * 启动随机动作
   */
  static startMotion(group: string, priority?: number): boolean {
    const lappDefine = getLAppDefine();
    const defaultPriority = lappDefine?.PriorityNormal || 2;
    return startRandomMotion(group, priority || defaultPriority);
  }

  /**
   * 播放音频并同步嘴型
   */
  static async playAudioWithLipSync(
    audioBase64: string,
    options: {
      expression?: string | number;
      motion?: string;
      onStart?: () => void;
      onEnd?: () => void;
      onError?: (error: Error) => void;
    } = {}
  ): Promise<void> {
    if (!isValidAudioBase64(audioBase64)) {
      const error = new Error('Invalid audio base64 data');
      options.onError?.(error);
      throw error;
    }

    return new Promise((resolve, reject) => {
      try {
        const manager = getLive2DManager();
        if (!manager) {
          const error = new Error('Live2D manager not available');
          options.onError?.(error);
          reject(error);
          return;
        }

        const model = manager.getModel(0);
        if (!model) {
          const error = new Error('Live2D model not available');
          options.onError?.(error);
          reject(error);
          return;
        }

        // 设置表情
        if (options.expression !== undefined) {
          this.setExpression(options.expression);
        }

        // 启动动作
        if (options.motion) {
          this.startMotion(options.motion);
        }

        const audioDataUrl = createAudioDataUrl(audioBase64);
        const audio = new Audio(audioDataUrl);

        audio.addEventListener('canplaythrough', () => {
          options.onStart?.();
          audio.play().catch(reject);

          // 启动嘴型同步
          if (model._wavFileHandler) {
            model._wavFileHandler.start(audioDataUrl);
          }
        });

        audio.addEventListener('ended', () => {
          options.onEnd?.();
          resolve();
        });

        audio.addEventListener('error', (error) => {
          const audioError = new Error('Audio playback failed');
          options.onError?.(audioError);
          reject(audioError);
        });

        audio.load();
      } catch (error) {
        const playbackError = error as Error;
        options.onError?.(playbackError);
        reject(playbackError);
      }
    });
  }

  /**
   * 批量播放音频队列
   */
  static async playAudioQueue(
    audioList: Array<{
      audioBase64: string;
      expression?: string | number;
      motion?: string;
      delay?: number;
    }>
  ): Promise<void> {
    for (const audioItem of audioList) {
      if (audioItem.delay) {
        await new Promise(resolve => setTimeout(resolve, audioItem.delay));
      }

      await this.playAudioWithLipSync(audioItem.audioBase64, {
        expression: audioItem.expression,
        motion: audioItem.motion,
      });
    }
  }

  /**
   * 停止所有音频播放
   */
  static stopAllAudio(): void {
    audioTaskQueue.clear();
    
    // 停止当前播放的音频
    const audios = document.querySelectorAll('audio');
    audios.forEach(audio => {
      audio.pause();
      audio.src = '';
    });

    // 重置模型的嘴型同步
    const model = this.getCurrentModel();
    if (model && model._wavFileHandler) {
      try {
        model._wavFileHandler.releasePcmData();
        model._wavFileHandler._lastRms = 0.0;
        model._wavFileHandler._sampleOffset = 0;
        model._wavFileHandler._userTimeSeconds = 0.0;
      } catch (error) {
        console.error('Error resetting wav file handler:', error);
      }
    }
  }

  /**
   * 获取可用表情列表
   */
  static getAvailableExpressions(): string[] {
    const adapter = getLAppAdapter();
    if (!adapter) return [];

    try {
      const expressionCount = adapter.getExpressionCount?.() || 0;
      const expressions: string[] = [];

      for (let i = 0; i < expressionCount; i++) {
        const expressionName = adapter.getExpressionName(i);
        if (expressionName) {
          expressions.push(expressionName);
        }
      }

      return expressions;
    } catch (error) {
      console.error('Failed to get expressions:', error);
      return [];
    }
  }

  /**
   * 获取可用动作组列表
   */
  static getAvailableMotionGroups(): string[] {
    const model = this.getCurrentModel();
    if (!model || !model._modelSetting) return [];

    try {
      // 这里需要根据实际的 Live2D SDK API 来获取动作组
      // 这是一个示例实现
      const motionGroups: string[] = [];
      
      if (model._modelSetting.getMotionGroupCount) {
        const groupCount = model._modelSetting.getMotionGroupCount();
        for (let i = 0; i < groupCount; i++) {
          const groupName = model._modelSetting.getMotionGroupName(i);
          if (groupName) {
            motionGroups.push(groupName);
          }
        }
      }

      return motionGroups;
    } catch (error) {
      console.error('Failed to get motion groups:', error);
      return ['Idle', 'Talk']; // 返回常见的默认动作组
    }
  }

  /**
   * 随机表情
   */
  static setRandomExpression(): boolean {
    const expressions = this.getAvailableExpressions();
    if (expressions.length === 0) return false;

    const randomIndex = Math.floor(Math.random() * expressions.length);
    return this.setExpression(expressions[randomIndex]);
  }

  /**
   * 随机动作
   */
  static startRandomMotionFromGroup(): boolean {
    const motionGroups = this.getAvailableMotionGroups();
    if (motionGroups.length === 0) return false;

    const randomGroup = motionGroups[Math.floor(Math.random() * motionGroups.length)];
    return this.startMotion(randomGroup);
  }

  /**
   * 重置模型到默认状态
   */
  static resetToDefault(modelInfo?: ModelInfo): void {
    // 停止所有音频
    this.stopAllAudio();

    // 重置表情
    if (modelInfo?.defaultEmotion !== undefined) {
      this.setExpression(modelInfo.defaultEmotion);
    } else {
      const expressions = this.getAvailableExpressions();
      if (expressions.length > 0) {
        this.setExpression(expressions[0]);
      }
    }

    // 启动空闲动作
    const idleGroup = modelInfo?.idleMotionGroupName || 'Idle';
    this.startMotion(idleGroup);
  }
}

// 导出便捷的全局函数
export const {
  isReady,
  getCurrentModel,
  setExpression,
  startMotion,
  playAudioWithLipSync,
  playAudioQueue,
  stopAllAudio,
  getAvailableExpressions,
  getAvailableMotionGroups,
  setRandomExpression,
  startRandomMotionFromGroup,
  resetToDefault,
} = Live2DHelper;
