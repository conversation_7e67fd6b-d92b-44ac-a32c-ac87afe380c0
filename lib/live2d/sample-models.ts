import { ModelInfo } from '@/types/live2d';

/**
 * 示例 Live2D 模型配置
 * 用户可以根据实际模型修改这些配置
 */

export const sampleModels: ModelInfo[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    description: "Official Live2D sample model",
    url: "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/hiyori/hiyori_free_en.model3.json",
    kScale: 1.0,
    initialXshift: 0,
    initialYshift: 0,
    idleMotionGroupName: "Idle",
    defaultEmotion: 0,
    emotionMap: {
      "normal": 0,
      "happy": 1,
      "sad": 2,
      "angry": 3,
    },
    pointerInteractive: true,
    scrollToResize: true,
    tapMotions: {
      "Head": {
        "TapHead": 1.0,
      },
      "Body": {
        "TapBody": 1.0,
      },
    },
    initialScale: 1.0,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    description: "Another sample model",
    url: "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/shizuku/shizuku.model3.json",
    kScale: 0.8,
    initialXshift: 0,
    initialYshift: 0,
    idleMotionGroupName: "Idle",
    defaultEmotion: "normal",
    emotionMap: {
      "normal": 0,
      "smile": 1,
      "wink": 2,
    },
    pointerInteractive: true,
    scrollToResize: true,
    tapMotions: {
      "Head": {
        "TapHead": 1.0,
      },
    },
    initialScale: 0.8,
  },
];

/**
 * 获取默认模型配置
 */
export function getDefaultModel(): ModelInfo {
  return sampleModels[0];
}

/**
 * 根据名称获取模型配置
 */
export function getModelByName(name: string): ModelInfo | undefined {
  return sampleModels.find(model => model.name === name);
}

/**
 * 创建自定义模型配置
 */
export function createCustomModel(
  url: string,
  options: Partial<ModelInfo> = {}
): ModelInfo {
  return {
    name: options.name || "Custom Model",
    description: options.description || "User provided model",
    url,
    kScale: options.kScale || 1.0,
    initialXshift: options.initialXshift || 0,
    initialYshift: options.initialYshift || 0,
    idleMotionGroupName: options.idleMotionGroupName || "Idle",
    defaultEmotion: options.defaultEmotion || 0,
    emotionMap: options.emotionMap || {},
    pointerInteractive: options.pointerInteractive !== false,
    scrollToResize: options.scrollToResize !== false,
    tapMotions: options.tapMotions || {},
    initialScale: options.initialScale || 1.0,
  };
}
