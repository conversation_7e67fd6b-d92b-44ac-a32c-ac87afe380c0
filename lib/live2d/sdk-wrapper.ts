/**
 * Live2D SDK 包装器
 * 提供对 Live2D WebSDK 的 TypeScript 接口
 */

import { Live2DModel, LAppAdapter, Live2DManager } from '@/types/live2d';

/**
 * Live2D SDK 初始化状态
 */
let isSDKInitialized = false;
let initializationPromise: Promise<void> | null = null;

/**
 * 动态加载 Live2D SDK 脚本
 */
async function loadSDKScripts(): Promise<void> {
  // 只加载核心脚本，其他功能通过我们的包装器实现
  const scripts = [
    '/live2d/WebSDK/Core/live2dcubismcore.min.js',
  ];

  for (const scriptSrc of scripts) {
    await new Promise<void>((resolve, reject) => {
      // 检查脚本是否已加载
      if (document.querySelector(`script[src="${scriptSrc}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = scriptSrc;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${scriptSrc}`));
      document.head.appendChild(script);
    });
  }
}

/**
 * 初始化自定义 Live2D 功能
 */
async function initializeCustomLive2D(): Promise<void> {
  // 设置全局访问函数
  if (typeof window !== 'undefined') {
    // 创建一个简单的模型管理器
    const modelManager = {
      models: [] as any[],
      getModel: (index: number) => modelManager.models[index] || null,
      addModel: (model: any) => {
        modelManager.models.push(model);
        return modelManager.models.length - 1;
      },
      removeModel: (index: number) => {
        if (index >= 0 && index < modelManager.models.length) {
          modelManager.models.splice(index, 1);
        }
      },
      clear: () => {
        modelManager.models = [];
      }
    };

    // 创建一个简单的适配器
    const adapter = {
      model: null as any,
      getModel: () => adapter.model,
      setModel: (model: any) => { adapter.model = model; },
      setExpression: (expression: string) => {
        if (adapter.model && adapter.model.setExpression) {
          adapter.model.setExpression(expression);
        }
      },
      getExpressionName: (index: number) => {
        if (adapter.model && adapter.model.getExpressionName) {
          return adapter.model.getExpressionName(index);
        }
        return null;
      },
      getExpressionCount: () => {
        if (adapter.model && adapter.model.getExpressionCount) {
          return adapter.model.getExpressionCount();
        }
        return 0;
      }
    };

    // 设置全局访问函数
    (window as any).getLive2DManager = () => modelManager;
    (window as any).getLAppAdapter = () => adapter;
    (window as any).LAppLive2DManager = {
      getInstance: () => modelManager,
      releaseInstance: () => modelManager.clear()
    };
    (window as any).LAppDefine = {
      PriorityNormal: 2,
      CurrentKScale: 1.0,
      updateModelConfig: (baseUrl: string, modelDir: string, modelFileName: string, kScale: number) => {
        console.log('Model config updated:', { baseUrl, modelDir, modelFileName, kScale });
        (window as any).LAppDefine.CurrentKScale = kScale;
      }
    };

    console.log('Custom Live2D manager initialized');
  }
}

/**
 * 初始化 Live2D SDK
 */
export async function initializeLive2DSDK(): Promise<void> {
  if (isSDKInitialized) {
    return;
  }

  if (initializationPromise) {
    return initializationPromise;
  }

  initializationPromise = (async () => {
    try {
      await loadSDKScripts();

      // 等待 Live2DCubismCore 可用
      let attempts = 0;
      const maxAttempts = 50;

      while (attempts < maxAttempts) {
        if (typeof window !== 'undefined' && (window as any).Live2DCubismCore) {
          break;
        }
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (attempts >= maxAttempts) {
        throw new Error('Live2DCubismCore not available');
      }

      // 初始化我们自己的 Live2D 管理器
      await initializeCustomLive2D();

      isSDKInitialized = true;
      console.log('Live2D SDK initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Live2D SDK:', error);
      initializationPromise = null;
      throw error;
    }
  })();

  return initializationPromise;
}

/**
 * 获取 Live2D 管理器实例
 */
export function getLive2DManager(): Live2DManager | null {
  if (typeof window === 'undefined') {
    return null;
  }

  return (window as any).getLive2DManager?.() || 
         (window as any).LAppLive2DManager?.getInstance() || 
         null;
}

/**
 * 获取 Live2D 适配器实例
 */
export function getLAppAdapter(): LAppAdapter | null {
  if (typeof window === 'undefined') {
    return null;
  }

  return (window as any).getLAppAdapter?.() || null;
}

/**
 * 获取指定索引的模型
 */
export function getModel(index = 0): Live2DModel | null {
  const manager = getLive2DManager();
  return manager?.getModel?.(index) || null;
}

/**
 * 更新模型配置
 */
export function updateModelConfig(
  baseUrl: string,
  modelDir: string,
  modelFileName: string,
  kScale: number
): void {
  if (typeof window !== 'undefined' && (window as any).updateModelConfig) {
    (window as any).updateModelConfig(baseUrl, modelDir, modelFileName, kScale);
  }
}

/**
 * 释放 Live2D 管理器实例
 */
export function releaseLive2DManager(): void {
  if (typeof window !== 'undefined' && (window as any).LAppLive2DManager?.releaseInstance) {
    (window as any).LAppLive2DManager.releaseInstance();
  }
}

/**
 * 检查 SDK 是否已初始化
 */
export function isSDKReady(): boolean {
  return isSDKInitialized;
}

/**
 * 重置 SDK 初始化状态（用于测试或重新初始化）
 */
export function resetSDKState(): void {
  isSDKInitialized = false;
  initializationPromise = null;
}

/**
 * 获取 Live2D 定义常量
 */
export function getLAppDefine() {
  if (typeof window === 'undefined') {
    return null;
  }

  return (window as any).LAppDefine || null;
}

/**
 * 播放音频并同步嘴型
 */
export async function playAudioWithLipSync(
  audioPath: string, 
  modelIndex = 0
): Promise<void> {
  return new Promise((resolve, reject) => {
    const manager = getLive2DManager();
    if (!manager) {
      reject(new Error('Live2D manager not initialized'));
      return;
    }

    const fullPath = `/live2d/Resources/${audioPath}`;
    const audio = new Audio(fullPath);

    audio.addEventListener('canplaythrough', () => {
      const model = manager.getModel(modelIndex);
      if (model) {
        if (model._wavFileHandler) {
          model._wavFileHandler.start(fullPath);
          audio.play();
        } else {
          reject(new Error('Wav file handler not available on model'));
        }
      } else {
        reject(new Error(`Model index ${modelIndex} not found`));
      }
    });

    audio.addEventListener('ended', () => {
      resolve();
    });

    audio.addEventListener('error', () => {
      reject(new Error(`Failed to load audio: ${fullPath}`));
    });

    audio.load();
  });
}

/**
 * 设置模型表情
 */
export function setModelExpression(
  expressionValue: string | number,
  modelIndex = 0
): boolean {
  const adapter = getLAppAdapter();
  if (!adapter) {
    console.error('LAppAdapter not available');
    return false;
  }

  try {
    if (typeof expressionValue === 'string') {
      adapter.setExpression(expressionValue);
    } else if (typeof expressionValue === 'number') {
      const expressionName = adapter.getExpressionName(expressionValue);
      if (expressionName) {
        adapter.setExpression(expressionName);
      }
    }
    return true;
  } catch (error) {
    console.error('Failed to set expression:', error);
    return false;
  }
}

/**
 * 启动随机动作
 */
export function startRandomMotion(
  group: string,
  priority: number,
  modelIndex = 0
): boolean {
  const manager = getLive2DManager();
  if (!manager) {
    return false;
  }

  const model = manager.getModel(modelIndex);
  if (!model) {
    return false;
  }

  try {
    model.startRandomMotion(group, priority);
    return true;
  } catch (error) {
    console.error('Failed to start random motion:', error);
    return false;
  }
}
