'use client';

import React, { createContext, useContext, useState, useMemo, ReactNode } from 'react';
import { ModelInfo, Live2DConfigState } from '@/types/live2d';

/**
 * 默认配置值
 */
const DEFAULT_CONFIG = {
  modelInfo: undefined,
  isLoading: false,
};

/**
 * Live2D 配置上下文
 */
export const Live2DConfigContext = createContext<Live2DConfigState | null>(null);

/**
 * Live2D 配置提供者组件属性
 */
interface Live2DConfigProviderProps {
  children: ReactNode;
}

/**
 * Live2D 配置提供者组件
 */
export function Live2DConfigProvider({ children }: Live2DConfigProviderProps) {
  const [modelInfo, setModelInfoState] = useState<ModelInfo | undefined>(DEFAULT_CONFIG.modelInfo);
  const [isLoading, setIsLoading] = useState(DEFAULT_CONFIG.isLoading);

  /**
   * 设置模型信息
   */
  const setModelInfo = (info: ModelInfo | undefined) => {
    if (!info?.url) {
      setModelInfoState(undefined);
      return;
    }

    // 使用传入的缩放值，如果没有则使用默认值
    const finalScale = Number(info.kScale || 0.5) * 2;
    console.log('Setting model info with scale:', finalScale);

    setModelInfoState({
      ...info,
      kScale: finalScale,
      // 保留其他用户可能修改的设置，否则使用默认值
      pointerInteractive: info.pointerInteractive ?? true,
      scrollToResize: info.scrollToResize ?? true,
    });
  };

  const contextValue = useMemo(
    () => ({
      modelInfo,
      setModelInfo,
      isLoading,
      setIsLoading,
    }),
    [modelInfo, isLoading]
  );

  return (
    <Live2DConfigContext.Provider value={contextValue}>
      {children}
    </Live2DConfigContext.Provider>
  );
}

/**
 * 使用 Live2D 配置的自定义 Hook
 */
export function useLive2DConfig() {
  const context = useContext(Live2DConfigContext);

  if (!context) {
    throw new Error('useLive2DConfig must be used within a Live2DConfigProvider');
  }

  return context;
}

/**
 * 默认导出配置提供者
 */
export default Live2DConfigProvider;
