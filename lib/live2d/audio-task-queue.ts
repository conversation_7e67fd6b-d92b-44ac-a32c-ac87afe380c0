/**
 * 音频任务队列管理器
 * 用于管理音频播放任务的顺序执行
 */

export class AudioTaskQueue {
  private queue: (() => Promise<void>)[] = [];
  private isProcessing = false;

  /**
   * 添加音频任务到队列
   */
  addTask(task: () => Promise<void>): void {
    this.queue.push(task);
    this.processQueue();
  }

  /**
   * 处理队列中的任务
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const task = this.queue.shift();
      if (task) {
        try {
          await task();
        } catch (error) {
          console.error('Audio task execution error:', error);
        }
      }
    }

    this.isProcessing = false;
  }

  /**
   * 等待所有任务完成
   */
  async waitForCompletion(): Promise<void> {
    while (this.isProcessing || this.queue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue = [];
  }

  /**
   * 获取队列长度
   */
  get length(): number {
    return this.queue.length;
  }

  /**
   * 检查是否正在处理
   */
  get processing(): boolean {
    return this.isProcessing;
  }
}

/**
 * 全局音频任务队列实例
 */
export const audioTaskQueue = new AudioTaskQueue();
