import { CanvasScale, Position } from '@/types/live2d';

/**
 * 解析模型 URL，提取基础 URL、模型目录和文件名
 */
export function parseModelUrl(url: string): { 
  baseUrl: string; 
  modelDir: string; 
  modelFileName: string; 
} {
  try {
    const urlObj = new URL(url);
    const { pathname } = urlObj;

    const lastSlashIndex = pathname.lastIndexOf('/');
    if (lastSlashIndex === -1) {
      throw new Error('Invalid model URL format');
    }

    const fullFileName = pathname.substring(lastSlashIndex + 1);
    const modelFileName = fullFileName.replace('.model3.json', '');

    const secondLastSlashIndex = pathname.lastIndexOf('/', lastSlashIndex - 1);
    if (secondLastSlashIndex === -1) {
      throw new Error('Invalid model URL format');
    }

    const modelDir = pathname.substring(secondLastSlashIndex + 1, lastSlashIndex);
    const baseUrl = `${urlObj.protocol}//${urlObj.host}${pathname.substring(0, secondLastSlashIndex + 1)}`;

    return { baseUrl, modelDir, modelFileName };
  } catch (error) {
    console.error('Error parsing model URL:', error);
    return { baseUrl: '', modelDir: '', modelFileName: '' };
  }
}

/**
 * 获取画布缩放信息
 */
export function getCanvasScale(canvasId = 'canvas'): CanvasScale {
  const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
  if (!canvas) return { width: 1, height: 1, scale: 1 };

  const { width, height } = canvas;
  const scale = width / canvas.clientWidth;

  return { width, height, scale };
}

/**
 * 将屏幕坐标转换为模型坐标
 */
export function screenToModelPosition(
  screenX: number, 
  screenY: number, 
  canvasScale: CanvasScale
): Position {
  const { width, height, scale } = canvasScale;

  const x = ((screenX * scale) / width) * 2 - 1;
  const y = -((screenY * scale) / height) * 2 + 1;

  return { x, y };
}

/**
 * 限制数值在指定范围内
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value));
}

/**
 * 线性插值
 */
export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * factor;
}

/**
 * 计算两点之间的距离
 */
export function distance(p1: Position, p2: Position): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 检查是否为有效的音频 Base64 数据
 */
export function isValidAudioBase64(audioBase64: string): boolean {
  if (!audioBase64 || typeof audioBase64 !== 'string') {
    return false;
  }

  // 基本的 Base64 格式检查
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  return base64Regex.test(audioBase64);
}

/**
 * 创建音频数据 URL
 */
export function createAudioDataUrl(audioBase64: string, mimeType = 'audio/wav'): string {
  return `data:${mimeType};base64,${audioBase64}`;
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 获取设备像素比
 */
export function getDevicePixelRatio(): number {
  return window.devicePixelRatio || 1;
}

/**
 * 检查是否为移动设备
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}

/**
 * 获取随机数（指定范围）
 */
export function randomInRange(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

/**
 * 格式化错误消息
 */
export function formatError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Unknown error occurred';
}
