'use client';

import React, { memo, useRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { Live2DProps, ModelInfo } from '@/types/live2d';
import { useLive2DConfig } from '@/lib/live2d/config-context';
import { useLive2DModel } from '@/hooks/live2d/use-live2d-model';
import { useLive2DResize } from '@/hooks/live2d/use-live2d-resize';
import { useLive2DExpression } from '@/hooks/live2d/use-live2d-expression';
import { 
  initializeLive2DSDK, 
  updateModelConfig, 
  releaseLive2DManager,
  getLAppAdapter 
} from '@/lib/live2d/sdk-wrapper';
import { parseModelUrl } from '@/lib/live2d/utils';

interface Live2DCanvasProps extends Live2DProps {
  modelInfo?: ModelInfo;
}

/**
 * Live2D 画布组件
 * 负责渲染 Live2D 模型并处理交互
 */
export const Live2DCanvas = memo<Live2DCanvasProps>(({
  className,
  style,
  modelInfo,
  onModelLoad,
  onModelError,
}) => {
  const { setIsLoading } = useLive2DConfig();
  const { resetExpression } = useLive2DExpression();
  
  const containerRef = useRef<HTMLDivElement>(null);
  const [isSDKReady, setIsSDKReady] = useState(false);
  const [currentModelUrl, setCurrentModelUrl] = useState<string | null>(null);

  // 获取画布引用和处理器
  const { canvasRef, handleResize } = useLive2DResize({
    containerRef,
    modelInfo,
  });

  const { isDragging, handlers } = useLive2DModel({
    modelInfo,
    canvasRef,
  });

  /**
   * 初始化 Live2D SDK
   */
  useEffect(() => {
    let isMounted = true;

    const initSDK = async () => {
      try {
        setIsLoading(true);
        await initializeLive2DSDK();
        
        if (isMounted) {
          setIsSDKReady(true);
          console.log('Live2D SDK initialized successfully');
        }
      } catch (error) {
        console.error('Failed to initialize Live2D SDK:', error);
        if (isMounted) {
          onModelError?.(error as Error);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    initSDK();

    return () => {
      isMounted = false;
    };
  }, [setIsLoading, onModelError]);

  /**
   * 处理模型加载
   */
  useEffect(() => {
    if (!isSDKReady || !modelInfo?.url) {
      return;
    }

    const loadModel = async () => {
      try {
        setIsLoading(true);

        // 检查是否需要更新模型
        if (currentModelUrl === modelInfo.url) {
          setIsLoading(false);
          return;
        }

        console.log('Loading Live2D model:', modelInfo.url);

        // 解析模型 URL
        const { baseUrl, modelDir, modelFileName } = parseModelUrl(modelInfo.url);
        
        if (!baseUrl || !modelDir) {
          throw new Error('Invalid model URL format');
        }

        // 更新模型配置
        updateModelConfig(baseUrl, modelDir, modelFileName, modelInfo.kScale);

        // 释放之前的模型实例
        releaseLive2DManager();

        // 等待一段时间让 SDK 重新初始化
        setTimeout(async () => {
          try {
            await initializeLive2DSDK();
            setCurrentModelUrl(modelInfo.url);
            
            // 重置表情到默认状态
            setTimeout(() => {
              const adapter = getLAppAdapter();
              if (adapter) {
                resetExpression(adapter, modelInfo);
              }
            }, 1000);

            onModelLoad?.();
            console.log('Model loaded successfully');
          } catch (error) {
            console.error('Failed to load model:', error);
            onModelError?.(error as Error);
          } finally {
            setIsLoading(false);
          }
        }, 500);

      } catch (error) {
        console.error('Error during model loading:', error);
        onModelError?.(error as Error);
        setIsLoading(false);
      }
    };

    loadModel();
  }, [
    isSDKReady, 
    modelInfo?.url, 
    modelInfo?.kScale, 
    currentModelUrl, 
    setIsLoading, 
    onModelLoad, 
    onModelError,
    resetExpression
  ]);

  /**
   * 处理指针按下事件
   */
  const handlePointerDown = (e: React.PointerEvent) => {
    // 转换为鼠标事件并传递给处理器
    const mouseEvent = e as unknown as React.MouseEvent;
    handlers.onMouseDown(mouseEvent);
  };

  /**
   * 处理右键菜单（可选功能）
   */
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    console.log('Right-click detected on Live2D model');
    // 这里可以添加右键菜单逻辑
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative w-full h-full overflow-hidden',
        className
      )}
      style={{
        ...style,
        cursor: isDragging ? 'grabbing' : 'default',
      }}
      onPointerDown={handlePointerDown}
      onContextMenu={handleContextMenu}
      {...handlers}
    >
      <canvas
        id="canvas"
        ref={canvasRef}
        className={cn(
          'block w-full h-full',
          isDragging ? 'cursor-grabbing' : 'cursor-default'
        )}
        style={{
          pointerEvents: 'auto',
        }}
      />
      
      {/* 加载指示器 */}
      {!isSDKReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50">
          <div className="text-sm text-gray-600">Loading Live2D...</div>
        </div>
      )}
    </div>
  );
});

Live2DCanvas.displayName = 'Live2DCanvas';
