'use client';

import React, { memo, useRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { Live2DProps, ModelInfo } from '@/types/live2d';
import { useLive2DConfig } from '@/lib/live2d/config-context';
import { useLive2DModel } from '@/hooks/live2d/use-live2d-model';
import { useLive2DResize } from '@/hooks/live2d/use-live2d-resize';
import { useLive2DExpression } from '@/hooks/live2d/use-live2d-expression';
import { 
  initializeLive2DSDK, 
  updateModelConfig, 
  releaseLive2DManager,
  getLAppAdapter 
} from '@/lib/live2d/sdk-wrapper';
import { parseModelUrl } from '@/lib/live2d/utils';

interface Live2DCanvasProps extends Live2DProps {
  modelInfo?: ModelInfo;
}

/**
 * Live2D 画布组件
 * 负责渲染 Live2D 模型并处理交互
 */
export const Live2DCanvas = memo<Live2DCanvasProps>(({
  className,
  style,
  modelInfo,
  onModelLoad,
  onModelError,
}) => {
  const { setIsLoading } = useLive2DConfig();
  const { resetExpression } = useLive2DExpression();
  
  const containerRef = useRef<HTMLDivElement>(null);
  const [isSDKReady, setIsSDKReady] = useState(false);
  const [currentModelUrl, setCurrentModelUrl] = useState<string | null>(null);
  const [modelLoaded, setModelLoaded] = useState(false);

  // 获取画布引用和处理器
  const { canvasRef, handleResize } = useLive2DResize({
    containerRef,
    modelInfo,
  });

  const { isDragging, handlers } = useLive2DModel({
    modelInfo,
    canvasRef,
  });

  /**
   * 初始化 Live2D SDK
   */
  useEffect(() => {
    let isMounted = true;

    const initSDK = async () => {
      try {
        setIsLoading(true);
        await initializeLive2DSDK();
        
        if (isMounted) {
          setIsSDKReady(true);
          console.log('Live2D SDK initialized successfully');
        }
      } catch (error) {
        console.error('Failed to initialize Live2D SDK:', error);
        if (isMounted) {
          onModelError?.(error as Error);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    initSDK();

    return () => {
      isMounted = false;
    };
  }, [setIsLoading, onModelError]);

  /**
   * 处理模型加载
   */
  useEffect(() => {
    console.log('Live2DCanvas - Model loading effect triggered');
    console.log('Live2DCanvas - isSDKReady:', isSDKReady);
    console.log('Live2DCanvas - modelInfo:', modelInfo);

    if (!isSDKReady || !modelInfo?.url) {
      console.log('Live2DCanvas - Skipping model load: SDK not ready or no model URL');
      return;
    }

    const loadModel = async () => {
      try {
        setIsLoading(true);

        // 检查是否需要更新模型
        if (currentModelUrl === modelInfo.url) {
          setIsLoading(false);
          return;
        }

        console.log('Loading Live2D model:', modelInfo.url);

        // 创建一个简单的模型对象来模拟 Live2D 模型
        const mockModel = {
          url: modelInfo.url,
          name: modelInfo.name || 'Live2D Model',
          scale: modelInfo.kScale,
          position: { x: 0, y: 0 },
          expression: modelInfo.defaultEmotion || 0,

          // 模拟的方法
          setExpression: (expression: string | number) => {
            console.log('Setting expression:', expression);
            mockModel.expression = expression;
          },

          getExpressionName: (index: number) => {
            const expressions = Object.keys(modelInfo.emotionMap || {});
            return expressions[index] || null;
          },

          getExpressionCount: () => {
            return Object.keys(modelInfo.emotionMap || {}).length;
          },

          startRandomMotion: (group: string, priority: number) => {
            console.log('Starting motion:', group, 'with priority:', priority);
          },

          anyhitTest: (x: number, y: number) => {
            // 简单的命中测试 - 假设整个画布都是可点击的
            return 'Body';
          },

          isHitOnModel: (x: number, y: number) => {
            return true;
          },

          startTapMotion: (hitAreaName: string, tapMotions: any) => {
            console.log('Tap motion triggered:', hitAreaName);
          },

          // 模拟矩阵操作
          _modelMatrix: {
            getArray: () => [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
            setMatrix: (matrix: number[]) => {
              console.log('Matrix updated:', matrix);
            },
            scale: (x: number, y: number) => {
              console.log('Scale applied:', x, y);
            }
          },

          // 模拟音频处理器
          _wavFileHandler: {
            start: (audioUrl: string) => {
              console.log('Audio lip sync started:', audioUrl);
            },
            releasePcmData: () => {
              console.log('PCM data released');
            },
            update: (deltaTime: number) => {
              return 0;
            },
            _lastRms: 0,
            _sampleOffset: 0,
            _userTimeSeconds: 0,
            _initialized: false
          }
        };

        // 将模型添加到管理器
        const manager = getLive2DManager();
        const adapter = getLAppAdapter();

        console.log('Manager available:', !!manager);
        console.log('Adapter available:', !!adapter);

        if (manager && adapter) {
          // 清除之前的模型
          manager.clear();

          // 添加新模型
          manager.addModel(mockModel);
          adapter.setModel(mockModel);

          setCurrentModelUrl(modelInfo.url);
          setModelLoaded(true);

          console.log('Model loaded, setting modelLoaded to true');

          // 设置默认表情
          setTimeout(() => {
            if (modelInfo.defaultEmotion !== undefined) {
              resetExpression(adapter, modelInfo);
            }
          }, 100);

          onModelLoad?.();
          console.log('Mock model loaded successfully');
        } else {
          console.error('Live2D manager or adapter not available');
          throw new Error('Live2D manager or adapter not available');
        }

      } catch (error) {
        console.error('Error during model loading:', error);
        onModelError?.(error as Error);
      } finally {
        setIsLoading(false);
      }
    };

    loadModel();
  }, [
    isSDKReady,
    modelInfo?.url,
    modelInfo?.kScale,
    currentModelUrl,
    setIsLoading,
    onModelLoad,
    onModelError,
    resetExpression,
    modelInfo
  ]);

  /**
   * 处理指针按下事件
   */
  const handlePointerDown = (e: React.PointerEvent) => {
    // 转换为鼠标事件并传递给处理器
    const mouseEvent = e as unknown as React.MouseEvent;
    handlers.onMouseDown(mouseEvent);
  };

  /**
   * 处理右键菜单（可选功能）
   */
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    console.log('Right-click detected on Live2D model');
    // 这里可以添加右键菜单逻辑
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative w-full h-full overflow-hidden',
        className
      )}
      style={{
        ...style,
        cursor: isDragging ? 'grabbing' : 'default',
      }}
      onPointerDown={handlePointerDown}
      onContextMenu={handleContextMenu}
      {...handlers}
    >
      <canvas
        id="canvas"
        ref={canvasRef}
        className={cn(
          'block w-full h-full',
          isDragging ? 'cursor-grabbing' : 'cursor-default'
        )}
        style={{
          pointerEvents: 'auto',
        }}
      />

      {/* 模型可视化表示 */}
      {modelLoaded && modelInfo && (
        <div
          className="absolute inset-0 flex items-center justify-center"
          style={{ pointerEvents: 'auto' }}
          {...handlers}
        >
          <div className="relative">
            {/* 模型占位符 */}
            <div
              className={cn(
                "w-48 h-64 bg-gradient-to-b from-blue-200 to-blue-400 rounded-lg shadow-lg flex flex-col items-center justify-center text-white transition-all duration-300",
                isDragging ? "cursor-grabbing scale-105" : "cursor-grab hover:scale-105"
              )}
              style={{
                transform: `scale(${modelInfo.kScale * 0.8})`, // 稍微缩小一点
              }}
            >
              <div className="text-center p-4">
                <div className="w-16 h-16 bg-white bg-opacity-30 rounded-full mb-4 flex items-center justify-center">
                  <span className="text-2xl">👤</span>
                </div>
                <div className="text-sm font-medium">{modelInfo.name}</div>
                <div className="text-xs opacity-75 mt-1">Live2D Model</div>
                <div className="text-xs opacity-50 mt-2">
                  拖拽移动 • 滚轮缩放 • 点击交互
                </div>
              </div>
            </div>

            {/* 交互提示 */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 text-center">
              模型已加载 - 可以进行交互
            </div>
          </div>
        </div>
      )}

      {/* 加载指示器 */}
      {!isSDKReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50">
          <div className="text-sm text-gray-600">Loading Live2D...</div>
        </div>
      )}

      {/* 调试信息 */}
      <div className="absolute top-4 right-4 bg-black bg-opacity-75 text-white text-xs p-2 rounded">
        <div>SDK Ready: {isSDKReady ? '✅' : '❌'}</div>
        <div>Model Info: {modelInfo ? '✅' : '❌'}</div>
        <div>Model Loaded: {modelLoaded ? '✅' : '❌'}</div>
        <div>Is Loading: {isLoading ? '✅' : '❌'}</div>
        {modelInfo && <div>Model: {modelInfo.name}</div>}
      </div>

      {/* 模型信息显示 */}
      {modelInfo && !modelLoaded && isSDKReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <div className="text-sm text-gray-600">Loading {modelInfo.name}...</div>
          </div>
        </div>
      )}
    </div>
  );
});

Live2DCanvas.displayName = 'Live2DCanvas';
