'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLive2DConfig } from '@/lib/live2d/config-context';
import { useLive2DExpression } from '@/hooks/live2d/use-live2d-expression';
import { useAudioTask } from '@/hooks/live2d/use-audio-task';
import { ModelInfo } from '@/types/live2d';
import { getLAppAdapter } from '@/lib/live2d/sdk-wrapper';

/**
 * Live2D 控制面板组件
 * 提供模型配置、表情控制、音频测试等功能
 */
export function Live2DControls() {
  const { modelInfo, setModelInfo } = useLive2DConfig();
  const { setExpression, resetExpression } = useLive2DExpression();
  const { playAudioWithLipSync, stopCurrentAudioAndLipSync, clearAudioQueue } = useAudioTask();

  const [modelUrl, setModelUrl] = useState(modelInfo?.url || '');
  const [kScale, setKScale] = useState(modelInfo?.kScale?.toString() || '1.0');
  const [expressionInput, setExpressionInput] = useState('');
  const [audioBase64, setAudioBase64] = useState('');

  /**
   * 加载模型
   */
  const handleLoadModel = useCallback(() => {
    if (!modelUrl.trim()) {
      alert('Please enter a model URL');
      return;
    }

    const newModelInfo: ModelInfo = {
      url: modelUrl.trim(),
      kScale: parseFloat(kScale) || 1.0,
      initialXshift: 0,
      initialYshift: 0,
      emotionMap: {},
      pointerInteractive: true,
      scrollToResize: true,
      tapMotions: {},
    };

    setModelInfo(newModelInfo);
  }, [modelUrl, kScale, setModelInfo]);

  /**
   * 设置表情
   */
  const handleSetExpression = useCallback(() => {
    if (!expressionInput.trim()) {
      alert('Please enter an expression name or index');
      return;
    }

    const adapter = getLAppAdapter();
    if (!adapter) {
      alert('Live2D adapter not available');
      return;
    }

    // 尝试解析为数字，如果失败则作为字符串处理
    const expressionValue = isNaN(Number(expressionInput)) 
      ? expressionInput.trim() 
      : Number(expressionInput);

    setExpression(expressionValue, adapter, `Setting expression: ${expressionValue}`);
  }, [expressionInput, setExpression]);

  /**
   * 重置表情
   */
  const handleResetExpression = useCallback(() => {
    const adapter = getLAppAdapter();
    if (!adapter) {
      alert('Live2D adapter not available');
      return;
    }

    resetExpression(adapter, modelInfo);
  }, [resetExpression, modelInfo]);

  /**
   * 播放音频测试
   */
  const handlePlayAudio = useCallback(async () => {
    if (!audioBase64.trim()) {
      alert('Please enter audio base64 data');
      return;
    }

    try {
      await playAudioWithLipSync(audioBase64.trim());
    } catch (error) {
      console.error('Audio playback error:', error);
      alert('Failed to play audio');
    }
  }, [audioBase64, playAudioWithLipSync]);

  /**
   * 停止音频
   */
  const handleStopAudio = useCallback(() => {
    stopCurrentAudioAndLipSync();
  }, [stopCurrentAudioAndLipSync]);

  /**
   * 清空音频队列
   */
  const handleClearQueue = useCallback(() => {
    clearAudioQueue();
  }, [clearAudioQueue]);

  return (
    <div className="space-y-4 p-4 max-w-md">
      {/* 模型配置 */}
      <Card>
        <CardHeader>
          <CardTitle>Model Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="model-url">Model URL</Label>
            <Input
              id="model-url"
              value={modelUrl}
              onChange={(e) => setModelUrl(e.target.value)}
              placeholder="https://example.com/model.model3.json"
            />
          </div>
          
          <div>
            <Label htmlFor="k-scale">Scale</Label>
            <Input
              id="k-scale"
              value={kScale}
              onChange={(e) => setKScale(e.target.value)}
              placeholder="1.0"
              type="number"
              step="0.1"
            />
          </div>
          
          <Button onClick={handleLoadModel} className="w-full">
            Load Model
          </Button>
        </CardContent>
      </Card>

      {/* 表情控制 */}
      <Card>
        <CardHeader>
          <CardTitle>Expression Control</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="expression">Expression (name or index)</Label>
            <Input
              id="expression"
              value={expressionInput}
              onChange={(e) => setExpressionInput(e.target.value)}
              placeholder="happy or 0"
            />
          </div>
          
          <div className="flex space-x-2">
            <Button onClick={handleSetExpression} className="flex-1">
              Set Expression
            </Button>
            <Button onClick={handleResetExpression} variant="outline" className="flex-1">
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 音频控制 */}
      <Card>
        <CardHeader>
          <CardTitle>Audio Control</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="audio-base64">Audio Base64</Label>
            <textarea
              id="audio-base64"
              value={audioBase64}
              onChange={(e) => setAudioBase64(e.target.value)}
              placeholder="Enter base64 audio data..."
              className="w-full h-20 p-2 border rounded resize-none text-sm"
            />
          </div>
          
          <div className="flex space-x-2">
            <Button onClick={handlePlayAudio} className="flex-1">
              Play Audio
            </Button>
            <Button onClick={handleStopAudio} variant="outline" className="flex-1">
              Stop
            </Button>
          </div>
          
          <Button onClick={handleClearQueue} variant="destructive" className="w-full">
            Clear Queue
          </Button>
        </CardContent>
      </Card>

      {/* 当前状态 */}
      {modelInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Current Model</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-1">
              <div><strong>URL:</strong> {modelInfo.url}</div>
              <div><strong>Scale:</strong> {modelInfo.kScale}</div>
              <div><strong>Interactive:</strong> {modelInfo.pointerInteractive ? 'Yes' : 'No'}</div>
              <div><strong>Resizable:</strong> {modelInfo.scrollToResize ? 'Yes' : 'No'}</div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
