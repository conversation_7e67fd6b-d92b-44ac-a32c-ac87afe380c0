'use client';

import React, { memo, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Live2DProps } from '@/types/live2d';
import { Live2DCanvas } from './live2d-canvas';
import { useLive2DConfig } from '@/lib/live2d/config-context';
import { useLive2DExpression } from '@/hooks/live2d/use-live2d-expression';
import { useAudioTask } from '@/hooks/live2d/use-audio-task';
import { getLAppAdapter } from '@/lib/live2d/sdk-wrapper';

/**
 * 主要的 Live2D 组件
 * 整合所有 Live2D 功能，包括模型显示、交互、音频同步等
 */
export const Live2D = memo<Live2DProps>(({
  className,
  style,
  modelInfo: propModelInfo,
  onModelLoad,
  onModelError,
}) => {
  const { modelInfo: contextModelInfo, isLoading } = useLive2DConfig();
  const { resetExpression } = useLive2DExpression();
  const { clearAudioQueue } = useAudioTask();

  // 使用传入的 modelInfo 或上下文中的 modelInfo
  const modelInfo = propModelInfo || contextModelInfo;

  // 调试信息
  console.log('Live2D component - modelInfo:', modelInfo);
  console.log('Live2D component - isLoading:', isLoading);

  /**
   * 当模型信息改变时重置表情
   */
  useEffect(() => {
    if (modelInfo) {
      // 延迟重置表情，确保模型已加载
      const timer = setTimeout(() => {
        const adapter = getLAppAdapter();
        if (adapter) {
          resetExpression(adapter, modelInfo);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [modelInfo, resetExpression]);

  /**
   * 组件卸载时清理音频队列
   */
  useEffect(() => {
    return () => {
      clearAudioQueue();
    };
  }, [clearAudioQueue]);

  /**
   * 处理模型加载成功
   */
  const handleModelLoad = () => {
    console.log('Live2D model loaded successfully');
    onModelLoad?.();
  };

  /**
   * 处理模型加载错误
   */
  const handleModelError = (error: Error) => {
    console.error('Live2D model load error:', error);
    onModelError?.(error);
  };

  if (!modelInfo) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center w-full h-full bg-gray-100',
          className
        )}
        style={style}
      >
        <div className="text-gray-500 text-sm">
          No Live2D model configured
        </div>
      </div>
    );
  }

  return (
    <div 
      className={cn('relative w-full h-full', className)}
      style={style}
    >
      <Live2DCanvas
        modelInfo={modelInfo}
        onModelLoad={handleModelLoad}
        onModelError={handleModelError}
        className="w-full h-full"
      />
      
      {/* 加载状态覆盖层 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
          <div className="flex flex-col items-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <div className="text-sm text-gray-600">Loading Live2D Model...</div>
          </div>
        </div>
      )}
    </div>
  );
});

Live2D.displayName = 'Live2D';
