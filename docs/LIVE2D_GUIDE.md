# Live2D 功能使用指南

本文档介绍如何在 AdvX 前端项目中使用 Live2D 功能。

## 功能概述

Live2D 功能已成功迁移到 Next.js 前端项目，支持以下特性：

- ✅ **模型显示**：支持 Live2D 模型的加载和渲染
- ✅ **拖拽移动**：点击并拖动模型在屏幕上任意移动
- ✅ **缩放调节**：使用鼠标滚轮调整模型大小
- ✅ **点击交互**：点击模型触发对应的 HitArea 动作
- ✅ **音频同步**：基于 motionsync 的嘴型同步功能
- ✅ **表情控制**：通过索引或字符串设置模型表情

## 快速开始

### 1. 基础使用

```tsx
import { Live2D } from '@/components/live2d/live2d';
import { Live2DConfigProvider } from '@/lib/live2d/config-context';

function App() {
  return (
    <Live2DConfigProvider>
      <div className="w-full h-screen">
        <Live2D 
          onModelLoad={() => console.log('模型加载完成')}
          onModelError={(error) => console.error('模型加载错误:', error)}
        />
      </div>
    </Live2DConfigProvider>
  );
}
```

### 2. 配置模型

```tsx
import { useLive2DConfig } from '@/lib/live2d/config-context';
import { ModelInfo } from '@/types/live2d';

function ModelSelector() {
  const { setModelInfo } = useLive2DConfig();

  const loadModel = () => {
    const modelInfo: ModelInfo = {
      name: "我的模型",
      url: "https://example.com/model.model3.json",
      kScale: 1.0,
      initialXshift: 0,
      initialYshift: 0,
      emotionMap: {
        "happy": 0,
        "sad": 1,
        "angry": 2,
      },
      pointerInteractive: true,
      scrollToResize: true,
      tapMotions: {
        "Head": { "TapHead": 1.0 },
        "Body": { "TapBody": 1.0 },
      },
    };
    
    setModelInfo(modelInfo);
  };

  return (
    <button onClick={loadModel}>
      加载模型
    </button>
  );
}
```

## 高级功能

### 1. 音频同步播放

```tsx
import { useAudioTask } from '@/hooks/live2d/use-audio-task';

function AudioPlayer() {
  const { playAudioWithLipSync } = useAudioTask();

  const playAudio = async () => {
    const audioBase64 = "你的音频base64数据";
    
    await playAudioWithLipSync(audioBase64, ["happy"]); // 播放时设置表情
  };

  return (
    <button onClick={playAudio}>
      播放音频
    </button>
  );
}
```

### 2. 表情控制

```tsx
import { useLive2DExpression } from '@/hooks/live2d/use-live2d-expression';
import { getLAppAdapter } from '@/lib/live2d/sdk-wrapper';

function ExpressionController() {
  const { setExpression, resetExpression } = useLive2DExpression();

  const changeExpression = () => {
    const adapter = getLAppAdapter();
    if (adapter) {
      setExpression("happy", adapter, "设置为开心表情");
    }
  };

  const resetToDefault = () => {
    const adapter = getLAppAdapter();
    if (adapter) {
      resetExpression(adapter);
    }
  };

  return (
    <div>
      <button onClick={changeExpression}>设置表情</button>
      <button onClick={resetToDefault}>重置表情</button>
    </div>
  );
}
```

### 3. 使用辅助工具类

```tsx
import { Live2DHelper } from '@/lib/live2d/live2d-helper';

function QuickActions() {
  const handlePlayAudio = async () => {
    try {
      await Live2DHelper.playAudioWithLipSync("音频base64数据", {
        expression: "happy",
        motion: "Talk",
        onStart: () => console.log("开始播放"),
        onEnd: () => console.log("播放结束"),
        onError: (error) => console.error("播放错误:", error),
      });
    } catch (error) {
      console.error("音频播放失败:", error);
    }
  };

  const setRandomExpression = () => {
    Live2DHelper.setRandomExpression();
  };

  const startRandomMotion = () => {
    Live2DHelper.startRandomMotionFromGroup();
  };

  return (
    <div>
      <button onClick={handlePlayAudio}>播放音频</button>
      <button onClick={setRandomExpression}>随机表情</button>
      <button onClick={startRandomMotion}>随机动作</button>
    </div>
  );
}
```

## 配置选项

### ModelInfo 接口

```typescript
interface ModelInfo {
  name?: string;                    // 模型名称
  description?: string;             // 模型描述
  url: string;                      // 模型 URL
  kScale: number;                   // 缩放因子
  initialXshift: number;            // 初始 X 偏移
  initialYshift: number;            // 初始 Y 偏移
  idleMotionGroupName?: string;     // 空闲动作组名称
  defaultEmotion?: number | string; // 默认表情
  emotionMap: EmotionMap;           // 表情映射
  pointerInteractive?: boolean;     // 启用指针交互
  tapMotions?: TapMotionMap;        // 点击动作映射
  scrollToResize?: boolean;         // 启用滚轮缩放
  initialScale?: number;            // 初始缩放
}
```

### 音频任务选项

```typescript
interface AudioTaskOptions {
  audioBase64: string;              // 音频 base64 数据
  volumes: number[];                // 音量数组
  sliceLength: number;              // 切片长度
  displayText?: DisplayText | null; // 显示文本
  expressions?: string[] | number[] | null; // 表情数组
  speaker_uid?: string;             // 说话者 ID
  forwarded?: boolean;              // 是否转发
}
```

## 测试功能

访问 `/test` 页面可以测试所有 Live2D 功能：

- 模型加载测试
- 表情控制测试
- 动作播放测试
- 音频同步测试
- 交互功能测试

## 注意事项

1. **模型 URL**：确保模型 URL 可访问且格式正确
2. **CORS 问题**：如果模型托管在其他域名，需要配置 CORS
3. **音频格式**：音频数据需要是有效的 base64 编码
4. **性能优化**：大型模型可能影响性能，建议适当调整缩放
5. **浏览器兼容性**：确保目标浏览器支持 WebGL

## 故障排除

### 常见问题

1. **模型不显示**
   - 检查模型 URL 是否正确
   - 检查浏览器控制台是否有错误
   - 确认 Live2D SDK 已正确加载

2. **音频不播放**
   - 检查音频 base64 数据是否有效
   - 确认浏览器允许音频播放
   - 检查音频格式是否支持

3. **交互不响应**
   - 确认 `pointerInteractive` 设置为 `true`
   - 检查模型是否有定义 HitArea
   - 确认事件处理器正确绑定

### 调试工具

使用浏览器开发者工具查看：
- 控制台错误信息
- 网络请求状态
- Live2D SDK 加载状态

## API 参考

详细的 API 文档请参考：
- `/types/live2d.ts` - 类型定义
- `/lib/live2d/sdk-wrapper.ts` - SDK 包装器
- `/lib/live2d/live2d-helper.ts` - 辅助工具类
- `/hooks/live2d/` - React Hooks

## 示例项目

查看 `/app/page.tsx` 和 `/app/test/page.tsx` 获取完整的使用示例。
