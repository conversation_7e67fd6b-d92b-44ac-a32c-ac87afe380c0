/**
 * Live2D 相关类型定义
 */

/**
 * 模型情感映射接口
 */
export interface EmotionMap {
  [key: string]: number | string;
}

/**
 * 动作权重映射接口
 */
export interface MotionWeightMap {
  [key: string]: number;
}

/**
 * 点击动作映射接口
 */
export interface TapMotionMap {
  [key: string]: MotionWeightMap;
}

/**
 * Live2D 模型信息接口
 */
export interface ModelInfo {
  /** 模型名称 */
  name?: string;

  /** 模型描述 */
  description?: string;

  /** 模型 URL */
  url: string;

  /** 缩放因子 */
  kScale: number;

  /** 初始 X 位置偏移 */
  initialXshift: number;

  /** 初始 Y 位置偏移 */
  initialYshift: number;

  /** 空闲动作组名称 */
  idleMotionGroupName?: string;

  /** 默认表情 */
  defaultEmotion?: number | string;

  /** 表情映射配置 */
  emotionMap: EmotionMap;

  /** 启用指针交互 */
  pointerInteractive?: boolean;

  /** 点击动作映射配置 */
  tapMotions?: TapMotionMap;

  /** 启用滚轮缩放 */
  scrollToResize?: boolean;

  /** 初始缩放 */
  initialScale?: number;
}

/**
 * 位置接口
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * 音频任务选项接口
 */
export interface AudioTaskOptions {
  audioBase64: string;
  volumes: number[];
  sliceLength: number;
  displayText?: DisplayText | null;
  expressions?: string[] | number[] | null;
  speaker_uid?: string;
  forwarded?: boolean;
}

/**
 * 显示文本接口
 */
export interface DisplayText {
  text: string;
  name?: string;
  avatar?: string;
}

/**
 * Live2D 模型实例类型（简化）
 */
export type Live2DModel = any;

/**
 * Live2D 适配器类型（简化）
 */
export type LAppAdapter = any;

/**
 * Live2D 管理器类型（简化）
 */
export type Live2DManager = any;

/**
 * 画布缩放信息接口
 */
export interface CanvasScale {
  width: number;
  height: number;
  scale: number;
}

/**
 * 鼠标/触摸事件处理器接口
 */
export interface MouseHandlers {
  onMouseDown: (e: React.MouseEvent) => void;
  onMouseMove: (e: React.MouseEvent) => void;
  onMouseUp: (e: React.MouseEvent) => void;
  onMouseLeave: () => void;
}

/**
 * Live2D 配置上下文状态接口
 */
export interface Live2DConfigState {
  modelInfo?: ModelInfo;
  setModelInfo: (info: ModelInfo | undefined) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

/**
 * Live2D 组件属性接口
 */
export interface Live2DProps {
  className?: string;
  style?: React.CSSProperties;
  modelInfo?: ModelInfo;
  onModelLoad?: () => void;
  onModelError?: (error: Error) => void;
}

/**
 * 音频任务队列接口
 */
export interface AudioTaskQueue {
  addTask: (task: () => Promise<void>) => void;
  waitForCompletion: () => Promise<void>;
  clear: () => void;
}

/**
 * Live2D 表情控制接口
 */
export interface Live2DExpressionControl {
  setExpression: (
    expressionValue: string | number,
    lappAdapter: LAppAdapter,
    logMessage?: string
  ) => void;
  resetExpression: (
    lappAdapter: LAppAdapter,
    modelInfo?: ModelInfo
  ) => void;
}

/**
 * Live2D 模型控制接口
 */
export interface Live2DModelControl {
  position: Position;
  isDragging: boolean;
  handlers: MouseHandlers;
}

/**
 * Live2D 缩放控制接口
 */
export interface Live2DResizeControl {
  canvasRef: React.RefObject<HTMLCanvasElement>;
  handleResize: () => void;
}

/**
 * 全局 Live2D 相关函数声明
 */
declare global {
  interface Window {
    getLive2DManager?: () => Live2DManager;
    getLAppAdapter?: () => LAppAdapter;
    LAppLive2DManager?: {
      getInstance: () => Live2DManager;
      releaseInstance: () => void;
    };
    LAppDefine?: {
      CurrentKScale?: number;
      PriorityNormal?: number;
    };
  }
}

export {};
