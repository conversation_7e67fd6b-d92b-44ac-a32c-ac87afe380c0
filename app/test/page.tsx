'use client';

import { useState, useEffect } from 'react';
import { Live2D } from '@/components/live2d/live2d';
import { Live2DConfigProvider, useLive2DConfig } from '@/lib/live2d/config-context';
import { Live2DHelper } from '@/lib/live2d/live2d-helper';
import { sampleModels, getDefaultModel } from '@/lib/live2d/sample-models';
import { ModelInfo } from '@/types/live2d';

/**
 * Live2D 测试页面组件
 */
function TestPageContent() {
  const { modelInfo, setModelInfo, isLoading } = useLive2DConfig();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isSDKReady, setIsSDKReady] = useState(false);

  /**
   * 添加测试结果
   */
  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  /**
   * 检查 SDK 状态
   */
  useEffect(() => {
    const checkSDK = () => {
      const ready = Live2DHelper.isReady();
      setIsSDKReady(ready);
      if (ready) {
        addTestResult('✅ Live2D SDK is ready');
      } else {
        addTestResult('❌ Live2D SDK not ready');
      }
    };

    const interval = setInterval(checkSDK, 1000);
    checkSDK();

    return () => clearInterval(interval);
  }, []);

  /**
   * 测试模型加载
   */
  const testModelLoading = () => {
    addTestResult('🔄 Testing model loading...');
    const defaultModel = getDefaultModel();
    addTestResult(`📋 Loading model: ${defaultModel.name}`);
    addTestResult(`📋 Model URL: ${defaultModel.url}`);
    setModelInfo(defaultModel);
  };

  /**
   * 测试表情设置
   */
  const testExpressions = () => {
    addTestResult('🔄 Testing expressions...');
    
    if (!Live2DHelper.isReady()) {
      addTestResult('❌ SDK not ready for expression test');
      return;
    }

    const expressions = Live2DHelper.getAvailableExpressions();
    addTestResult(`📋 Available expressions: ${expressions.join(', ')}`);

    if (expressions.length > 0) {
      const success = Live2DHelper.setExpression(expressions[0]);
      addTestResult(success ? '✅ Expression set successfully' : '❌ Failed to set expression');
    }
  };

  /**
   * 测试随机表情
   */
  const testRandomExpression = () => {
    addTestResult('🔄 Testing random expression...');
    const success = Live2DHelper.setRandomExpression();
    addTestResult(success ? '✅ Random expression set' : '❌ Failed to set random expression');
  };

  /**
   * 测试动作
   */
  const testMotions = () => {
    addTestResult('🔄 Testing motions...');
    
    const motionGroups = Live2DHelper.getAvailableMotionGroups();
    addTestResult(`📋 Available motion groups: ${motionGroups.join(', ')}`);

    if (motionGroups.length > 0) {
      const success = Live2DHelper.startMotion(motionGroups[0]);
      addTestResult(success ? '✅ Motion started successfully' : '❌ Failed to start motion');
    }
  };

  /**
   * 测试音频播放（使用示例 base64 数据）
   */
  const testAudioPlayback = async () => {
    addTestResult('🔄 Testing audio playback...');
    
    // 这是一个非常短的示例音频 base64（实际使用时需要真实的音频数据）
    const sampleAudioBase64 = 'UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
    
    try {
      await Live2DHelper.playAudioWithLipSync(sampleAudioBase64, {
        expression: 0,
        motion: 'Talk',
        onStart: () => addTestResult('🎵 Audio playback started'),
        onEnd: () => addTestResult('✅ Audio playback completed'),
        onError: (error) => addTestResult(`❌ Audio error: ${error.message}`),
      });
    } catch (error) {
      addTestResult(`❌ Audio test failed: ${(error as Error).message}`);
    }
  };

  /**
   * 测试重置功能
   */
  const testReset = () => {
    addTestResult('🔄 Testing reset...');
    Live2DHelper.resetToDefault(modelInfo);
    addTestResult('✅ Reset completed');
  };

  /**
   * 清空测试结果
   */
  const clearResults = () => {
    setTestResults([]);
  };

  /**
   * 运行所有测试
   */
  const runAllTests = async () => {
    addTestResult('🚀 Starting comprehensive test suite...');
    
    // 等待 SDK 准备就绪
    if (!isSDKReady) {
      addTestResult('⏳ Waiting for SDK to be ready...');
      return;
    }

    testExpressions();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testMotions();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testRandomExpression();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testAudioPlayback();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testReset();
    
    addTestResult('🎉 All tests completed!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm p-4">
        <h1 className="text-2xl font-bold text-gray-800">Live2D 功能测试</h1>
        <p className="text-gray-600 mt-1">测试拖拽、缩放、点击、音频同步等功能</p>
      </header>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Live2D 显示区域 */}
        <div className="flex-1 relative">
          <Live2D
            className="w-full h-full"
            onModelLoad={() => addTestResult('✅ Model loaded successfully')}
            onModelError={(error) => addTestResult(`❌ Model error: ${error.message}`)}
          />
          
          {/* 状态指示器 */}
          <div className="absolute top-4 left-4 bg-white rounded-lg p-3 shadow-lg">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isSDKReady ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm font-medium">
                {isSDKReady ? 'SDK Ready' : 'SDK Loading...'}
              </span>
            </div>
            {isLoading && (
              <div className="flex items-center space-x-2 mt-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500 animate-pulse"></div>
                <span className="text-sm">Model Loading...</span>
              </div>
            )}
          </div>

          {/* 操作说明 */}
          <div className="absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-lg max-w-sm">
            <h3 className="font-semibold text-gray-800 mb-2">操作说明</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 拖拽：点击并拖动模型</li>
              <li>• 缩放：使用鼠标滚轮</li>
              <li>• 点击：点击模型触发动作</li>
              <li>• 右侧面板：测试各种功能</li>
            </ul>
          </div>
        </div>

        {/* 测试控制面板 */}
        <div className="w-80 bg-white shadow-lg overflow-y-auto">
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-4">测试控制面板</h2>
            
            {/* 模型选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择模型
              </label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                onChange={(e) => {
                  const selectedModel = sampleModels.find(m => m.name === e.target.value);
                  if (selectedModel) {
                    setModelInfo(selectedModel);
                  }
                }}
                value={modelInfo?.name || ''}
              >
                <option value="">选择一个模型...</option>
                {sampleModels.map((model) => (
                  <option key={model.name} value={model.name}>
                    {model.name}
                  </option>
                ))}
              </select>
            </div>

            {/* 测试按钮 */}
            <div className="space-y-2 mb-4">
              <button
                onClick={testModelLoading}
                className="w-full p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              >
                测试模型加载
              </button>
              <button
                onClick={testExpressions}
                className="w-full p-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                disabled={!isSDKReady}
              >
                测试表情
              </button>
              <button
                onClick={testMotions}
                className="w-full p-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
                disabled={!isSDKReady}
              >
                测试动作
              </button>
              <button
                onClick={testAudioPlayback}
                className="w-full p-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
                disabled={!isSDKReady}
              >
                测试音频同步
              </button>
              <button
                onClick={testReset}
                className="w-full p-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                disabled={!isSDKReady}
              >
                重置模型
              </button>
              <button
                onClick={runAllTests}
                className="w-full p-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                disabled={!isSDKReady}
              >
                运行所有测试
              </button>
            </div>

            {/* 测试结果 */}
            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-gray-800">测试结果</h3>
                <button
                  onClick={clearResults}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  清空
                </button>
              </div>
              <div className="bg-gray-50 rounded-md p-3 h-64 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 text-sm">暂无测试结果</p>
                ) : (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-xs font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 测试页面
 */
export default function TestPage() {
  return (
    <Live2DConfigProvider>
      <TestPageContent />
    </Live2DConfigProvider>
  );
}
