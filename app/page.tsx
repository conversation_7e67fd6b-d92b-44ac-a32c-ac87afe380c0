'use client';

import { useState } from 'react';
import { Live2D } from '@/components/live2d/live2d';
import { Live2DConfigProvider, useLive2DConfig } from '@/lib/live2d/config-context';
import { getDefaultModel } from '@/lib/live2d/sample-models';

function HomeContent() {
  const [showControls, setShowControls] = useState(false);
  const { setModelInfo } = useLive2DConfig();

  const loadDefaultModel = () => {
    const defaultModel = getDefaultModel();
    setModelInfo(defaultModel);
  };

  // 示例模型配置（暂时未使用，等待用户配置）
  // const defaultModelInfo: ModelInfo = {
  //   name: "Default Live2D Model",
  //   url: "http://127.0.0.1:12393/live2d-models/elaina/LSS.model3.json",
  //   kScale: 1.0,
  //   initialXshift: 0,
  //   initialYshift: 0,
  //   emotionMap: {},
  //   pointerInteractive: true,
  //   scrollToResize: true,
  //   tapMotions: {},
  // };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* 头部 */}
        <header className="relative z-10 p-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">
              AdvX Live2D Demo
            </h1>
            <div className="flex space-x-2">
              <button
                onClick={loadDefaultModel}
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                Load Demo Model
              </button>
              <button
                onClick={() => setShowControls(!showControls)}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                {showControls ? 'Hide Controls' : 'Show Controls'}
              </button>
            </div>
          </div>
        </header>

        {/* 主要内容区域 */}
        <div className="flex h-[calc(100vh-80px)]">
          {/* Live2D 显示区域 */}
          <div className="flex-1 relative">
            <Live2D
              className="w-full h-full"
              onModelLoad={() => console.log('Model loaded!')}
              onModelError={(error) => console.error('Model error:', error)}
            />

            {/* 使用说明覆盖层 */}
            <div className="absolute top-4 left-4 bg-white bg-opacity-90 rounded-lg p-4 max-w-sm">
              <h3 className="font-semibold text-gray-800 mb-2">Live2D 功能演示</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 拖拽：点击并拖动模型进行移动</li>
                <li>• 缩放：使用鼠标滚轮调整模型大小</li>
                <li>• 点击：点击模型触发动作</li>
                <li>• 音频同步：播放音频时自动同步嘴型</li>
                <li>• 表情控制：通过控制面板设置表情</li>
              </ul>
              <p className="text-xs text-gray-500 mt-2">
                点击右上角&quot;Show Controls&quot;打开控制面板
              </p>
            </div>
          </div>

          {/* 控制面板（可选显示） */}
          {showControls && (
            <div className="w-80 bg-white shadow-lg overflow-y-auto">
              {/* 这里会放置控制面板组件，等 UI 组件准备好后取消注释 */}
              {/* <Live2DControls /> */}
              <div className="p-4">
                <h3 className="font-semibold mb-4">Live2D Controls</h3>
                <p className="text-sm text-gray-600">
                  控制面板正在开发中...
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  需要先添加 shadcn UI 组件
                </p>
              </div>
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <footer className="relative z-10 p-4 text-center text-sm text-gray-600">
          <p>Live2D 功能已成功迁移到 Next.js 前端项目</p>
        </footer>
    </div>
  );
}

export default function Home() {
  return (
    <Live2DConfigProvider>
      <HomeContent />
    </Live2DConfigProvider>
  );
}
