/**
 * Live2D 初始化脚本
 * 在浏览器环境中初始化 Live2D SDK
 */

// 全局变量
let lappDelegate = null;
let lappAdapter = null;
let isInitialized = false;

/**
 * 初始化 Live2D
 */
async function initializeLive2D() {
  if (isInitialized) {
    console.log('Live2D already initialized');
    return;
  }

  try {
    console.log('Initializing Live2D...');

    // 检查 Live2D Core 是否已加载
    if (typeof Live2DCubismCore === 'undefined') {
      throw new Error('Live2DCubismCore not loaded');
    }

    // 动态导入 Live2D 模块（如果使用 ES6 模块）
    // 这里假设 WebSDK 文件已经编译并可用

    // 初始化委托
    if (typeof LAppDelegate !== 'undefined') {
      lappDelegate = LAppDelegate.getInstance();
      if (lappDelegate && typeof lappDelegate.initialize === 'function') {
        await lappDelegate.initialize();
      }
    }

    // 设置全局访问函数
    window.getLive2DManager = function() {
      return window.LAppLive2DManager ? window.LAppLive2DManager.getInstance() : null;
    };

    window.getLAppAdapter = function() {
      return lappAdapter;
    };

    // 更新模型配置的函数
    window.updateModelConfig = function(baseUrl, modelDir, modelFileName, kScale) {
      if (window.LAppDefine && typeof window.LAppDefine.updateModelConfig === 'function') {
        window.LAppDefine.updateModelConfig(baseUrl, modelDir, modelFileName, kScale);
      }
    };

    isInitialized = true;
    console.log('Live2D initialized successfully');

  } catch (error) {
    console.error('Failed to initialize Live2D:', error);
    throw error;
  }
}

/**
 * 创建适配器
 */
function createLAppAdapter() {
  if (typeof LAppAdapter !== 'undefined') {
    lappAdapter = new LAppAdapter();
    return lappAdapter;
  }
  return null;
}

/**
 * 获取模型
 */
function getModel(index = 0) {
  const manager = window.getLive2DManager();
  return manager ? manager.getModel(index) : null;
}

/**
 * 设置表情
 */
function setExpression(expressionValue, modelIndex = 0) {
  const adapter = window.getLAppAdapter();
  if (!adapter) return false;

  try {
    if (typeof expressionValue === 'string') {
      adapter.setExpression(expressionValue);
    } else if (typeof expressionValue === 'number') {
      const expressionName = adapter.getExpressionName(expressionValue);
      if (expressionName) {
        adapter.setExpression(expressionName);
      }
    }
    return true;
  } catch (error) {
    console.error('Failed to set expression:', error);
    return false;
  }
}

/**
 * 启动随机动作
 */
function startRandomMotion(group, priority, modelIndex = 0) {
  const model = getModel(modelIndex);
  if (!model) return false;

  try {
    model.startRandomMotion(group, priority);
    return true;
  } catch (error) {
    console.error('Failed to start random motion:', error);
    return false;
  }
}

// 导出函数到全局作用域
if (typeof window !== 'undefined') {
  window.initializeLive2D = initializeLive2D;
  window.createLAppAdapter = createLAppAdapter;
  window.getModel = getModel;
  window.setExpression = setExpression;
  window.startRandomMotion = startRandomMotion;
}

// 如果在模块环境中，也导出这些函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    initializeLive2D,
    createLAppAdapter,
    getModel,
    setExpression,
    startRandomMotion
  };
}
