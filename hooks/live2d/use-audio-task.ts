'use client';

import { useRef, useCallback } from 'react';
import { AudioTaskOptions, DisplayText, Live2DModel } from '@/types/live2d';
import { audioTaskQueue } from '@/lib/live2d/audio-task-queue';
import { getLive2DManager, getLAppAdapter, getLAppDefine } from '@/lib/live2d/sdk-wrapper';
import { useLive2DExpression } from './use-live2d-expression';
import { createAudioDataUrl, isValidAudioBase64 } from '@/lib/live2d/utils';

/**
 * 音频任务 Hook
 * 处理音频播放和 Live2D 嘴型同步
 */
export function useAudioTask() {
  const { setExpression } = useLive2DExpression();
  
  // 跟踪当前音频和模型
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);
  const currentModelRef = useRef<Live2DModel | null>(null);

  /**
   * 停止当前音频播放和嘴型同步
   */
  const stopCurrentAudioAndLipSync = useCallback(() => {
    if (currentAudioRef.current) {
      console.log('Stopping current audio and lip sync');
      const audio = currentAudioRef.current;
      audio.pause();
      audio.src = '';
      audio.load();

      const model = currentModelRef.current;
      if (model && model._wavFileHandler) {
        try {
          // 释放 PCM 数据以停止 update() 中的嘴型同步计算
          model._wavFileHandler.releasePcmData();
          console.log('Called _wavFileHandler.releasePcmData()');

          // 额外重置状态变量作为后备
          model._wavFileHandler._lastRms = 0.0;
          model._wavFileHandler._sampleOffset = 0;
          model._wavFileHandler._userTimeSeconds = 0.0;
          console.log('Reset wavFileHandler state variables');
        } catch (e) {
          console.error('Error stopping/resetting wavFileHandler:', e);
        }
      } else if (model) {
        console.warn('Current model does not have _wavFileHandler to stop/reset.');
      } else {
        console.log('No associated model found to stop lip sync.');
      }

      currentAudioRef.current = null;
      currentModelRef.current = null;
    } else {
      console.log('No current audio playing to stop.');
    }
  }, []);

  /**
   * 处理音频播放和 Live2D 嘴型同步
   */
  const handleAudioPlayback = useCallback((options: AudioTaskOptions): Promise<void> => {
    return new Promise((resolve) => {
      const { audioBase64, displayText, expressions } = options;

      // 验证音频数据
      if (!audioBase64 || !isValidAudioBase64(audioBase64)) {
        console.warn('Invalid audio data provided');
        resolve();
        return;
      }

      try {
        // 处理音频
        const audioDataUrl = createAudioDataUrl(audioBase64);

        // 获取 Live2D 管理器和模型
        const live2dManager = getLive2DManager();
        if (!live2dManager) {
          console.error('Live2D manager not found');
          resolve();
          return;
        }

        const model = live2dManager.getModel(0);
        if (!model) {
          console.error('Live2D model not found at index 0');
          resolve();
          return;
        }

        console.log('Found model for audio playback');
        currentModelRef.current = model;

        if (!model._wavFileHandler) {
          console.warn('Model does not have _wavFileHandler for lip sync');
        } else {
          console.log('Model has _wavFileHandler available');
        }

        // 设置表情（如果提供）
        const lappAdapter = getLAppAdapter();
        if (lappAdapter && expressions?.[0] !== undefined) {
          setExpression(
            expressions[0],
            lappAdapter,
            `Set expression to: ${expressions[0]}`,
          );
        }

        // 启动说话动作
        const lappDefine = getLAppDefine();
        if (lappDefine && lappDefine.PriorityNormal) {
          console.log("Starting random 'Talk' motion");
          model.startRandomMotion(
            "Talk",
            lappDefine.PriorityNormal,
          );
        } else {
          console.warn("LAppDefine.PriorityNormal not found - cannot start talk motion");
        }

        // 设置音频元素
        const audio = new Audio(audioDataUrl);
        currentAudioRef.current = audio;
        let isFinished = false;

        const cleanup = () => {
          if (currentAudioRef.current === audio) {
            currentAudioRef.current = null;
            currentModelRef.current = null;
          }
          if (!isFinished) {
            isFinished = true;
            resolve();
          }
        };

        // 增强嘴型同步敏感度
        const lipSyncScale = 2.0;

        audio.addEventListener('canplaythrough', () => {
          console.log('Starting audio playback with lip sync');
          audio.play().catch((err) => {
            console.error("Audio play error:", err);
            cleanup();
          });

          // 设置嘴型同步
          if (model._wavFileHandler) {
            if (!model._wavFileHandler._initialized) {
              console.log('Applying enhanced lip sync');
              model._wavFileHandler._initialized = true;

              const originalUpdate = model._wavFileHandler.update.bind(model._wavFileHandler);
              model._wavFileHandler.update = function (deltaTimeSeconds: number) {
                const result = originalUpdate(deltaTimeSeconds);
                // 增强嘴型同步效果
                this._lastRms = Math.min(2.0, this._lastRms * lipSyncScale);
                return result;
              };
            }

            if (currentAudioRef.current === audio) {
              model._wavFileHandler.start(audioDataUrl);
            } else {
              console.warn('WavFileHandler start skipped - audio was stopped');
            }
          }
        });

        audio.addEventListener('ended', () => {
          console.log("Audio playback completed");
          cleanup();
        });

        audio.addEventListener('error', (error) => {
          console.error("Audio playback error:", error);
          cleanup();
        });

        audio.load();
      } catch (error) {
        console.error('Audio playback setup error:', error);
        currentAudioRef.current = null;
        currentModelRef.current = null;
        resolve();
      }
    });
  }, [setExpression]);

  /**
   * 添加音频任务到队列
   */
  const addAudioTask = useCallback(async (options: AudioTaskOptions) => {
    console.log(`Adding audio task to queue`);
    audioTaskQueue.addTask(() => handleAudioPlayback(options));
  }, [handleAudioPlayback]);

  /**
   * 播放音频并同步嘴型（简化版本）
   */
  const playAudioWithLipSync = useCallback(async (
    audioBase64: string,
    expressions?: string[] | number[]
  ) => {
    const options: AudioTaskOptions = {
      audioBase64,
      volumes: [],
      sliceLength: 0,
      expressions,
    };

    await addAudioTask(options);
  }, [addAudioTask]);

  /**
   * 清空音频任务队列
   */
  const clearAudioQueue = useCallback(() => {
    audioTaskQueue.clear();
    stopCurrentAudioAndLipSync();
  }, [stopCurrentAudioAndLipSync]);

  /**
   * 等待所有音频任务完成
   */
  const waitForAudioCompletion = useCallback(async () => {
    await audioTaskQueue.waitForCompletion();
  }, []);

  return {
    addAudioTask,
    playAudioWithLipSync,
    stopCurrentAudioAndLipSync,
    clearAudioQueue,
    waitForAudioCompletion,
  };
}
