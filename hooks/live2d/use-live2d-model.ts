'use client';

import { useCallback, useRef, useState, RefObject } from 'react';
import { Position, MouseHandlers, ModelInfo, Live2DModelControl } from '@/types/live2d';
import { getLAppAdapter } from '@/lib/live2d/sdk-wrapper';

// 点击和拖拽检测的阈值
const TAP_DURATION_THRESHOLD_MS = 200; // 点击的最大持续时间
const DRAG_DISTANCE_THRESHOLD_PX = 5; // 拖拽的最小距离

interface UseLive2DModelProps {
  modelInfo?: ModelInfo;
  canvasRef: RefObject<HTMLCanvasElement>;
}

/**
 * Live2D 模型控制 Hook
 * 处理模型的拖拽、点击交互等功能
 */
export function useLive2DModel({
  modelInfo,
  canvasRef,
}: UseLive2DModelProps): Live2DModelControl {
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });

  // 拖拽相关的引用
  const dragStartPos = useRef<Position>({ x: 0, y: 0 }); // 屏幕坐标
  const modelStartPos = useRef<Position>({ x: 0, y: 0 }); // 模型坐标
  const modelPositionRef = useRef<Position>({ x: 0, y: 0 });

  // 点击检测相关的引用
  const mouseDownTimeRef = useRef<number>(0);
  const mouseDownPosRef = useRef<Position>({ x: 0, y: 0 });
  const isPotentialTapRef = useRef<boolean>(false);

  /**
   * 获取模型当前位置
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getModelPosition = useCallback((): Position => {
    const adapter = getLAppAdapter();
    if (adapter) {
      const model = adapter.getModel();
      if (model && model._modelMatrix) {
        const matrix = model._modelMatrix.getArray();
        return {
          x: matrix[12],
          y: matrix[13],
        };
      }
    }
    return { x: 0, y: 0 };
  }, []);

  /**
   * 设置模型位置
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const setModelPosition = useCallback((x: number, y: number) => {
    const adapter = getLAppAdapter();
    if (adapter) {
      const model = adapter.getModel();
      if (model && model._modelMatrix) {
        const matrix = model._modelMatrix.getArray();
        const newMatrix = [...matrix];
        newMatrix[12] = x;
        newMatrix[13] = y;
        model._modelMatrix.setMatrix(newMatrix);
        modelPositionRef.current = { x, y };
      }
    }
  }, []);

  /**
   * 处理鼠标按下事件
   */
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const adapter = getLAppAdapter();
    if (!adapter || !canvasRef.current) return;

    const model = adapter.getModel();
    if (!model) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 检查是否点击在模型上
    const scale = canvas.width / canvas.clientWidth;
    const scaledX = x * scale;
    const scaledY = y * scale;

    // 这里需要根据实际的 Live2D SDK API 来检测点击
    // 假设有 hitTest 方法
    const hitAreaName = model.anyhitTest ? model.anyhitTest(scaledX, scaledY) : null;
    const isHitOnModel = model.isHitOnModel ? model.isHitOnModel(scaledX, scaledY) : true;

    if (hitAreaName !== null || isHitOnModel) {
      // 记录潜在的点击/拖拽开始
      mouseDownTimeRef.current = Date.now();
      mouseDownPosRef.current = { x: e.clientX, y: e.clientY };
      isPotentialTapRef.current = true;
      setIsDragging(false);

      // 存储初始模型位置以备拖拽使用
      if (model._modelMatrix) {
        const matrix = model._modelMatrix.getArray();
        modelStartPos.current = { x: matrix[12], y: matrix[13] };
      }
    }
  }, [canvasRef]);

  /**
   * 处理鼠标移动事件
   */
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const adapter = getLAppAdapter();
    const model = adapter?.getModel();

    // 开始拖拽逻辑
    if (isPotentialTapRef.current && adapter && model && canvasRef.current) {
      const timeElapsed = Date.now() - mouseDownTimeRef.current;
      const deltaX = e.clientX - mouseDownPosRef.current.x;
      const deltaY = e.clientY - mouseDownPosRef.current.y;
      const distanceMoved = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 检查是否为拖拽（移动距离足够或持续时间足够长）
      if (distanceMoved > DRAG_DISTANCE_THRESHOLD_PX || 
          (timeElapsed > TAP_DURATION_THRESHOLD_MS && distanceMoved > 1)) {
        isPotentialTapRef.current = false;
        setIsDragging(true);

        // 设置初始拖拽屏幕位置
        const canvas = canvasRef.current;
        const rect = canvas.getBoundingClientRect();
        dragStartPos.current = {
          x: mouseDownPosRef.current.x - rect.left,
          y: mouseDownPosRef.current.y - rect.top,
        };
      }
    }

    // 继续拖拽逻辑
    if (isDragging && adapter && model && canvasRef.current) {
      const canvas = canvasRef.current;
      const rect = canvas.getBoundingClientRect();
      const currentX = e.clientX - rect.left;
      const currentY = e.clientY - rect.top;

      // 计算拖拽偏移量
      const deltaX = currentX - dragStartPos.current.x;
      const deltaY = currentY - dragStartPos.current.y;

      // 转换为模型坐标系的偏移量
      const scale = canvas.width / canvas.clientWidth;
      const modelDeltaX = (deltaX * scale) / canvas.width * 2;
      const modelDeltaY = -(deltaY * scale) / canvas.height * 2;

      const newX = modelStartPos.current.x + modelDeltaX;
      const newY = modelStartPos.current.y + modelDeltaY;

      // 使用适配器的方法设置位置，或直接更新矩阵
      if (adapter.setModelPosition) {
        adapter.setModelPosition(newX, newY);
      } else if (model._modelMatrix) {
        const matrix = model._modelMatrix.getArray();
        const newMatrix = [...matrix];
        newMatrix[12] = newX;
        newMatrix[13] = newY;
        model._modelMatrix.setMatrix(newMatrix);
      }

      modelPositionRef.current = { x: newX, y: newY };
      setPosition({ x: newX, y: newY });
    }
  }, [isDragging, canvasRef]);

  /**
   * 处理鼠标抬起事件
   */
  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    const adapter = getLAppAdapter();
    const model = adapter?.getModel();

    if (isDragging) {
      // 完成拖拽
      setIsDragging(false);
      if (adapter && model && model._modelMatrix) {
        const matrix = model._modelMatrix.getArray();
        const finalPos = { x: matrix[12], y: matrix[13] };
        modelPositionRef.current = finalPos;
        modelStartPos.current = finalPos;
        setPosition(finalPos);
      }
    } else if (isPotentialTapRef.current && adapter && model && canvasRef.current) {
      // 点击动作逻辑
      const timeElapsed = Date.now() - mouseDownTimeRef.current;
      const deltaX = e.clientX - mouseDownPosRef.current.x;
      const deltaY = e.clientY - mouseDownPosRef.current.y;
      const distanceMoved = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 检查是否符合点击条件
      if (timeElapsed < TAP_DURATION_THRESHOLD_MS && 
          distanceMoved < DRAG_DISTANCE_THRESHOLD_PX) {
        const allowTapMotion = modelInfo?.pointerInteractive !== false;

        if (allowTapMotion && modelInfo?.tapMotions) {
          // 使用鼠标按下位置进行命中测试
          const canvas = canvasRef.current;
          const rect = canvas.getBoundingClientRect();
          const scale = canvas.width / canvas.clientWidth;
          const downX = (mouseDownPosRef.current.x - rect.left) * scale;
          const downY = (mouseDownPosRef.current.y - rect.top) * scale;

          const hitAreaName = model.anyhitTest ? model.anyhitTest(downX, downY) : null;
          
          // 触发点击动作
          if (model.startTapMotion) {
            model.startTapMotion(hitAreaName, modelInfo.tapMotions);
          }
        }
      }
    }

    // 重置潜在点击标志
    isPotentialTapRef.current = false;
  }, [isDragging, canvasRef, modelInfo]);

  /**
   * 处理鼠标离开事件
   */
  const handleMouseLeave = useCallback(() => {
    if (isDragging) {
      // 如果正在拖拽且鼠标离开，结束拖拽
      setIsDragging(false);
    }
    
    // 重置潜在点击标志
    if (isPotentialTapRef.current) {
      isPotentialTapRef.current = false;
    }
  }, [isDragging]);

  const handlers: MouseHandlers = {
    onMouseDown: handleMouseDown,
    onMouseMove: handleMouseMove,
    onMouseUp: handleMouseUp,
    onMouseLeave: handleMouseLeave,
  };

  return {
    position,
    isDragging,
    handlers,
  };
}
