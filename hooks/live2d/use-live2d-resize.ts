'use client';

import { useEffect, useCallback, RefObject, useRef } from 'react';
import { ModelInfo, Live2DResizeControl } from '@/types/live2d';
import { getLive2DManager } from '@/lib/live2d/sdk-wrapper';
import { clamp, lerp, debounce } from '@/lib/live2d/utils';

// 缩放相关常量
const MIN_SCALE = 0.1;
const MAX_SCALE = 5.0;
const EASING_FACTOR = 0.3; // 动画平滑度控制
const WHEEL_SCALE_STEP = 0.03; // 每次滚轮缩放的步长
const DEFAULT_SCALE = 1.0; // 默认缩放

interface UseLive2DResizeProps {
  containerRef: RefObject<HTMLDivElement>;
  modelInfo?: ModelInfo;
}

/**
 * 应用缩放到模型和视图矩阵
 */
export function applyScale(scale: number): void {
  try {
    const manager = getLive2DManager();
    if (!manager) return;

    const model = manager.getModel(0);
    if (!model) return;

    // 应用缩放到模型矩阵
    if (model._modelMatrix) {
      model._modelMatrix.scale(scale, scale);
    }
  } catch (error) {
    console.debug('Model not ready for scaling yet');
  }
}

/**
 * Live2D 缩放控制 Hook
 * 处理模型的缩放和窗口大小调整
 */
export function useLive2DResize({
  containerRef,
  modelInfo,
}: UseLive2DResizeProps): Live2DResizeControl {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const isResizingRef = useRef<boolean>(false);

  // 缩放状态引用
  const initialScale = modelInfo?.kScale || DEFAULT_SCALE;
  const lastScaleRef = useRef<number>(initialScale);
  const targetScaleRef = useRef<number>(initialScale);
  const animationFrameRef = useRef<number>();
  const isAnimatingRef = useRef<boolean>(false);
  const hasAppliedInitialScale = useRef<boolean>(false);

  // 上一次容器尺寸
  const lastContainerDimensionsRef = useRef<{ width: number; height: number }>({ 
    width: 0, 
    height: 0 
  });

  /**
   * 重置缩放状态（当模型改变时）
   */
  useEffect(() => {
    const newInitialScale = modelInfo?.kScale || DEFAULT_SCALE;
    lastScaleRef.current = newInitialScale;
    targetScaleRef.current = newInitialScale;
    hasAppliedInitialScale.current = false;

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      isAnimatingRef.current = false;
    }

    const resizeHandle = requestAnimationFrame(() => {
      handleResize();
    });

    return () => cancelAnimationFrame(resizeHandle);
  }, [modelInfo?.url, modelInfo?.kScale]);

  /**
   * 平滑缩放动画循环
   */
  const animateEase = useCallback(() => {
    const clampedTargetScale = clamp(targetScaleRef.current, MIN_SCALE, MAX_SCALE);
    const currentScale = lastScaleRef.current;
    const diff = clampedTargetScale - currentScale;

    const newScale = currentScale + diff * EASING_FACTOR;
    applyScale(newScale);
    lastScaleRef.current = newScale;

    // 如果还没有达到目标缩放，继续动画
    if (Math.abs(diff) > 0.001) {
      animationFrameRef.current = requestAnimationFrame(animateEase);
    } else {
      isAnimatingRef.current = false;
    }
  }, []);

  /**
   * 处理滚轮事件进行缩放
   */
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault();
    if (!modelInfo?.scrollToResize) return;

    const direction = e.deltaY > 0 ? -1 : 1;
    const increment = WHEEL_SCALE_STEP * direction;

    const currentActualScale = lastScaleRef.current;
    const newTargetScale = clamp(
      currentActualScale + increment,
      MIN_SCALE,
      MAX_SCALE
    );

    targetScaleRef.current = newTargetScale;

    if (!isAnimatingRef.current) {
      isAnimatingRef.current = true;
      animationFrameRef.current = requestAnimationFrame(animateEase);
    }
  }, [modelInfo?.scrollToResize, animateEase]);

  /**
   * 预处理容器大小调整
   */
  const beforeResize = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    isResizingRef.current = true;

    if (animationFrameIdRef.current !== null) {
      cancelAnimationFrame(animationFrameIdRef.current);
      animationFrameIdRef.current = null;
    }
  }, []);

  /**
   * 处理窗口/容器大小调整事件
   */
  const handleResize = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return;
    }

    if (!isResizingRef.current) {
      beforeResize();
    }

    try {
      const containerBounds = containerRef.current?.getBoundingClientRect();
      const { width, height } = containerBounds || { width: 0, height: 0 };

      const lastDimensions = lastContainerDimensionsRef.current;
      const dimensionsChanged = 
        Math.abs(lastDimensions.width - width) > 1 || 
        Math.abs(lastDimensions.height - height) > 1;

      if (!dimensionsChanged && hasAppliedInitialScale.current) {
        isResizingRef.current = false;
        return;
      }

      lastContainerDimensionsRef.current = { width, height };

      if (!containerBounds) {
        console.warn('[Resize] Container bounds not available.');
      }
      
      if (width === 0 || height === 0) {
        console.warn('[Resize] Width or Height is zero, skipping canvas update.');
        isResizingRef.current = false;
        return;
      }

      const dpr = window.devicePixelRatio || 1;
      canvas.width = Math.round(width * dpr);
      canvas.height = Math.round(height * dpr);
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;

      // 通知 Live2D 委托进行大小调整
      if (typeof window !== 'undefined' && (window as any).LAppDelegate) {
        const delegate = (window as any).LAppDelegate.getInstance();
        if (delegate && delegate.onResize) {
          delegate.onResize();
        }
      }

      isResizingRef.current = false;
    } catch (error) {
      console.error('Error during resize:', error);
      isResizingRef.current = false;
    }
  }, [containerRef, beforeResize]);

  // 防抖的 resize 处理器
  const debouncedHandleResize = useCallback(
    debounce(handleResize, 16), // ~60fps
    [handleResize]
  );

  // 设置滚轮事件监听器
  useEffect(() => {
    const canvasElement = canvasRef.current;
    if (canvasElement) {
      canvasElement.addEventListener('wheel', handleWheel, { passive: false });
      return () => {
        canvasElement.removeEventListener('wheel', handleWheel);
      };
    }
    return undefined;
  }, [handleWheel]);

  // 清理动画帧
  useEffect(() => () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = undefined;
    }
    if (animationFrameIdRef.current !== null) {
      cancelAnimationFrame(animationFrameIdRef.current);
      animationFrameIdRef.current = null;
    }
  }, []);

  // 监听容器大小变化
  useEffect(() => {
    const containerElement = containerRef.current;
    if (!containerElement) {
      return undefined;
    }

    // 初始调整大小
    if (animationFrameIdRef.current !== null) {
      cancelAnimationFrame(animationFrameIdRef.current);
    }
    animationFrameIdRef.current = requestAnimationFrame(() => {
      handleResize();
      animationFrameIdRef.current = null;
    });

    const observer = new ResizeObserver(() => {
      if (!isResizingRef.current) {
        if (animationFrameIdRef.current !== null) {
          cancelAnimationFrame(animationFrameIdRef.current);
        }
        animationFrameIdRef.current = requestAnimationFrame(() => {
          debouncedHandleResize();
          animationFrameIdRef.current = null;
        });
      }
    });

    observer.observe(containerElement);

    return () => {
      if (animationFrameIdRef.current !== null) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
      observer.disconnect();
    };
  }, [containerRef, debouncedHandleResize]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleWindowResize = () => {
      if (!isResizingRef.current) {
        if (animationFrameIdRef.current !== null) {
          cancelAnimationFrame(animationFrameIdRef.current);
        }
        animationFrameIdRef.current = requestAnimationFrame(() => {
          debouncedHandleResize();
          animationFrameIdRef.current = null;
        });
      }
    };

    window.addEventListener('resize', handleWindowResize);

    return () => {
      window.removeEventListener('resize', handleWindowResize);
      if (animationFrameIdRef.current !== null) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
    };
  }, [debouncedHandleResize]);

  return { canvasRef, handleResize };
}
