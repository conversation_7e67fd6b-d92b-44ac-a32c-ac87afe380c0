'use client';

import { useCallback } from 'react';
import { ModelInfo, Live2DExpressionControl, LAppAdapter } from '@/types/live2d';
import { getLAppAdapter } from '@/lib/live2d/sdk-wrapper';

/**
 * Live2D 表情控制 Hook
 * 处理模型表情的设置和重置
 */
export function useLive2DExpression(): Live2DExpressionControl {
  /**
   * 设置表情
   * @param expressionValue - 表情名称（字符串）或索引（数字）
   * @param lappAdapter - LAppAdapter 实例
   * @param logMessage - 可选的日志消息
   */
  const setExpression = useCallback((
    expressionValue: string | number,
    lappAdapter?: LAppAdapter,
    logMessage?: string,
  ) => {
    const adapter = lappAdapter || getLAppAdapter();
    if (!adapter) {
      console.error('LAppAdapter not available for setting expression');
      return;
    }

    try {
      if (typeof expressionValue === 'string') {
        // 通过名称设置表情
        adapter.setExpression(expressionValue);
      } else if (typeof expressionValue === 'number') {
        // 通过索引设置表情
        const expressionName = adapter.getExpressionName(expressionValue);
        if (expressionName) {
          adapter.setExpression(expressionName);
        } else {
          console.warn(`Expression index ${expressionValue} not found`);
          return;
        }
      } else {
        console.warn('Invalid expression value type:', typeof expressionValue);
        return;
      }

      if (logMessage) {
        console.log(logMessage);
      }
    } catch (error) {
      console.error('Failed to set expression:', error);
    }
  }, []);

  /**
   * 重置表情到默认状态
   * @param lappAdapter - LAppAdapter 实例
   * @param modelInfo - 当前模型信息
   */
  const resetExpression = useCallback((
    lappAdapter?: LAppAdapter,
    modelInfo?: ModelInfo,
  ) => {
    const adapter = lappAdapter || getLAppAdapter();
    if (!adapter) {
      console.error('LAppAdapter not available for resetting expression');
      return;
    }

    try {
      // 检查模型是否已加载并具有表情
      const model = adapter.getModel();
      if (!model || !model._modelSetting) {
        console.log('Model or model settings not loaded yet, skipping expression reset');
        return;
      }

      // 如果模型定义了默认表情，使用它
      if (modelInfo?.defaultEmotion !== undefined) {
        setExpression(
          modelInfo.defaultEmotion,
          adapter,
          `Reset expression to default: ${modelInfo.defaultEmotion}`,
        );
      } else {
        // 检查模型是否有表情，然后尝试获取第一个
        const expressionCount = adapter.getExpressionCount ? adapter.getExpressionCount() : 0;
        if (expressionCount > 0) {
          const defaultExpressionName = adapter.getExpressionName(0);
          if (defaultExpressionName) {
            setExpression(
              defaultExpressionName,
              adapter,
              `Reset expression to first available: ${defaultExpressionName}`,
            );
          }
        } else {
          console.log('No expressions available for this model');
        }
      }
    } catch (error) {
      console.error('Failed to reset expression:', error);
    }
  }, [setExpression]);

  /**
   * 获取可用表情列表
   * @param lappAdapter - LAppAdapter 实例
   * @returns 表情名称数组
   */
  const getAvailableExpressions = useCallback((
    lappAdapter?: LAppAdapter,
  ): string[] => {
    const adapter = lappAdapter || getLAppAdapter();
    if (!adapter) {
      console.error('LAppAdapter not available for getting expressions');
      return [];
    }

    try {
      const expressionCount = adapter.getExpressionCount ? adapter.getExpressionCount() : 0;
      const expressions: string[] = [];

      for (let i = 0; i < expressionCount; i++) {
        const expressionName = adapter.getExpressionName(i);
        if (expressionName) {
          expressions.push(expressionName);
        }
      }

      return expressions;
    } catch (error) {
      console.error('Failed to get available expressions:', error);
      return [];
    }
  }, []);

  /**
   * 检查表情是否存在
   * @param expressionValue - 表情名称或索引
   * @param lappAdapter - LAppAdapter 实例
   * @returns 是否存在该表情
   */
  const hasExpression = useCallback((
    expressionValue: string | number,
    lappAdapter?: LAppAdapter,
  ): boolean => {
    const adapter = lappAdapter || getLAppAdapter();
    if (!adapter) {
      return false;
    }

    try {
      if (typeof expressionValue === 'string') {
        const expressions = getAvailableExpressions(adapter);
        return expressions.includes(expressionValue);
      } else if (typeof expressionValue === 'number') {
        const expressionCount = adapter.getExpressionCount ? adapter.getExpressionCount() : 0;
        return expressionValue >= 0 && expressionValue < expressionCount;
      }
      return false;
    } catch (error) {
      console.error('Failed to check expression existence:', error);
      return false;
    }
  }, [getAvailableExpressions]);

  /**
   * 随机设置表情
   * @param lappAdapter - LAppAdapter 实例
   * @param excludeExpressions - 要排除的表情列表
   */
  const setRandomExpression = useCallback((
    lappAdapter?: LAppAdapter,
    excludeExpressions: (string | number)[] = [],
  ) => {
    const adapter = lappAdapter || getLAppAdapter();
    if (!adapter) {
      console.error('LAppAdapter not available for setting random expression');
      return;
    }

    try {
      const expressions = getAvailableExpressions(adapter);
      const availableExpressions = expressions.filter(expr => 
        !excludeExpressions.includes(expr)
      );

      if (availableExpressions.length === 0) {
        console.warn('No available expressions for random selection');
        return;
      }

      const randomIndex = Math.floor(Math.random() * availableExpressions.length);
      const randomExpression = availableExpressions[randomIndex];

      setExpression(
        randomExpression,
        adapter,
        `Set random expression: ${randomExpression}`,
      );
    } catch (error) {
      console.error('Failed to set random expression:', error);
    }
  }, [getAvailableExpressions, setExpression]);

  return {
    setExpression,
    resetExpression,
  };
}
